<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Household Budget Manager</title>
    <link rel="stylesheet" href="styles.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
</head>
<body>
    <div id="app">
        <!-- Header -->
        <header class="header">
            <div class="container">
                <h1 class="logo">💰 Household Budget Manager</h1>
                <nav class="nav">
                    <button class="nav-btn active" data-view="dashboard">Dashboard</button>
                    <button class="nav-btn" data-view="expenses">Expenses</button>
                    <button class="nav-btn" data-view="categories">Categories</button>
                    <button class="nav-btn" data-view="reports">Reports</button>
                    <button class="nav-btn" data-view="settings">Settings</button>
                </nav>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main">
            <div class="container">
                <!-- Dashboard View -->
                <div id="dashboard-view" class="view active">
                    <div class="view-header">
                        <h2>Budget Dashboard</h2>
                        <div class="actions">
                            <button class="btn btn-primary" onclick="loadExcelFile()">
                                📁 Load Excel File
                            </button>
                            <button class="btn btn-secondary" onclick="exportData()">
                                💾 Export Data
                            </button>
                        </div>
                    </div>

                    <!-- Budget Overview Cards -->
                    <div class="cards-grid">
                        <div class="card">
                            <div class="card-header">
                                <h3>Projected Balance</h3>
                                <span class="card-icon">📊</span>
                            </div>
                            <div class="card-content">
                                <div class="amount" id="projected-balance">$0</div>
                                <div class="subtitle">Monthly projection</div>
                            </div>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <h3>Actual Balance</h3>
                                <span class="card-icon">💵</span>
                            </div>
                            <div class="card-content">
                                <div class="amount" id="actual-balance">$0</div>
                                <div class="subtitle">Current balance</div>
                            </div>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <h3>Total Expenses</h3>
                                <span class="card-icon">💸</span>
                            </div>
                            <div class="card-content">
                                <div class="amount" id="total-expenses">$0</div>
                                <div class="subtitle">This month</div>
                            </div>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <h3>Savings</h3>
                                <span class="card-icon">🏦</span>
                            </div>
                            <div class="card-content">
                                <div class="amount" id="savings">$0</div>
                                <div class="subtitle">Projected vs Actual</div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Expenses -->
                    <div class="section">
                        <h3>Recent Expenses</h3>
                        <div class="table-container">
                            <table class="table" id="recent-expenses-table">
                                <thead>
                                    <tr>
                                        <th>Description</th>
                                        <th>Category</th>
                                        <th>Projected</th>
                                        <th>Actual</th>
                                        <th>Difference</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Dynamic content -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Expenses View -->
                <div id="expenses-view" class="view">
                    <div class="view-header">
                        <h2>Manage Expenses</h2>
                        <div class="actions">
                            <button class="btn btn-primary" onclick="showAddExpenseModal()">
                                ➕ Add Expense
                            </button>
                        </div>
                    </div>

                    <!-- Expenses Table -->
                    <div class="table-container">
                        <table class="table" id="expenses-table">
                            <thead>
                                <tr>
                                    <th>Description</th>
                                    <th>Category</th>
                                    <th>Projected Cost</th>
                                    <th>Actual Cost</th>
                                    <th>Difference</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Dynamic content -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Categories View -->
                <div id="categories-view" class="view">
                    <div class="view-header">
                        <h2>Manage Categories</h2>
                        <div class="actions">
                            <button class="btn btn-primary" onclick="showAddCategoryModal()">
                                ➕ Add Category
                            </button>
                        </div>
                    </div>

                    <div class="categories-grid" id="categories-grid">
                        <!-- Dynamic content -->
                    </div>
                </div>

                <!-- Reports View -->
                <div id="reports-view" class="view">
                    <div class="view-header">
                        <h2>Budget Reports</h2>
                    </div>

                    <div class="charts-grid">
                        <div class="chart-container">
                            <h3>Expenses by Category</h3>
                            <canvas id="category-chart"></canvas>
                        </div>
                        <div class="chart-container">
                            <h3>Projected vs Actual</h3>
                            <canvas id="comparison-chart"></canvas>
                        </div>
                    </div>
                </div>

                <!-- Settings View -->
                <div id="settings-view" class="view">
                    <div class="view-header">
                        <h2>Settings</h2>
                    </div>

                    <div class="settings-section">
                        <h3>Data Management</h3>
                        <div class="setting-item">
                            <label>Import Excel File:</label>
                            <input type="file" id="excel-file-input" accept=".xlsx,.xls" />
                        </div>
                        <div class="setting-item">
                            <button class="btn btn-secondary" onclick="clearAllData()">
                                🗑️ Clear All Data
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Modals -->
    <div id="add-expense-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Add New Expense</h3>
                <button class="modal-close" onclick="closeModal('add-expense-modal')">&times;</button>
            </div>
            <div class="modal-body">
                <form id="add-expense-form">
                    <div class="form-group">
                        <label for="expense-description">Description:</label>
                        <input type="text" id="expense-description" required>
                    </div>
                    <div class="form-group">
                        <label for="expense-category">Category:</label>
                        <select id="expense-category" required>
                            <!-- Dynamic options -->
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="expense-projected">Projected Cost:</label>
                        <input type="number" id="expense-projected" step="0.01" min="0">
                    </div>
                    <div class="form-group">
                        <label for="expense-actual">Actual Cost:</label>
                        <input type="number" id="expense-actual" step="0.01" min="0">
                    </div>
                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" onclick="closeModal('add-expense-modal')">Cancel</button>
                        <button type="submit" class="btn btn-primary">Add Expense</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div id="add-category-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Add New Category</h3>
                <button class="modal-close" onclick="closeModal('add-category-modal')">&times;</button>
            </div>
            <div class="modal-body">
                <form id="add-category-form">
                    <div class="form-group">
                        <label for="category-name">Category Name:</label>
                        <input type="text" id="category-name" required>
                    </div>
                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" onclick="closeModal('add-category-modal')">Cancel</button>
                        <button type="submit" class="btn btn-primary">Add Category</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="loading-overlay">
        <div class="loading-spinner"></div>
        <div class="loading-text">Loading...</div>
    </div>

    <script src="app.js"></script>
</body>
</html>
