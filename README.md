# Household Budget Manager

A modern, responsive Single Page Application (SPA) for managing household budgets, built with vanilla HTML, CSS, and JavaScript.

## Features

### 📊 Dashboard
- Budget overview with projected vs actual balance
- Total expenses and savings calculation
- Recent expenses display
- Visual cards showing key metrics

### 💰 Expense Management
- Add, edit, and delete expenses
- Categorize expenses
- Track projected vs actual costs
- Calculate differences automatically

### 🏷️ Category Management
- Create and manage expense categories
- View category statistics
- Delete unused categories

### 📈 Reports & Analytics
- Interactive charts showing expenses by category
- Projected vs actual cost comparisons
- Visual data representation using Chart.js

### 📁 Data Import/Export
- Load existing Excel budget files
- Export data as JSON
- Automatic parsing of Excel budget structure
- Local storage for data persistence

## Getting Started

### Prerequisites
- Modern web browser with JavaScript enabled
- Local web server (for file access)

### Installation

1. Clone or download the project files
2. Start a local web server in the project directory:
   ```bash
   # Using Python
   python -m http.server 8000
   
   # Using Node.js
   npx http-server
   
   # Using PHP
   php -S localhost:8000
   ```
3. Open your browser and navigate to `http://localhost:8000`

### Loading Your Excel Budget

The application can automatically load the included `Household monthly budget1.xlsx` file, or you can:

1. Click "Load Excel File" on the dashboard
2. Select your Excel file using the file picker
3. The app will parse and import your budget data

## Excel File Structure

The application expects Excel files with the following sheets:

### Budget Overview
- Contains projected and actual balance information
- Format: Labels in column B, values in column D

### Monthly Expenses
- Detailed expense items with descriptions, categories, and costs
- Headers: Description, Category, Projected cost, Actual cost, Difference

### Additional Data
- Category lists and additional configuration
- Used for category management

## File Structure

```
├── index.html          # Main application HTML
├── styles.css          # Application styles
├── app.js             # Main application logic
├── analyzer.html      # Excel file analyzer tool
├── analyze-excel.js   # Node.js Excel analysis script
├── test.html          # Application testing page
└── README.md          # This file
```

## Technologies Used

- **HTML5** - Semantic markup and structure
- **CSS3** - Modern styling with Grid and Flexbox
- **Vanilla JavaScript** - Application logic and DOM manipulation
- **SheetJS (xlsx)** - Excel file parsing and processing
- **Chart.js** - Interactive charts and data visualization
- **Local Storage** - Client-side data persistence

## Browser Compatibility

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## Features in Detail

### Dashboard
The dashboard provides a quick overview of your budget status with:
- Projected vs actual balance comparison
- Total monthly expenses
- Calculated savings
- Recent expense transactions

### Expense Management
- **Add Expenses**: Create new expense entries with description, category, and costs
- **Edit Expenses**: Modify existing expense details
- **Delete Expenses**: Remove unwanted expense entries
- **Categorization**: Organize expenses by categories for better tracking

### Category Management
- **Add Categories**: Create custom expense categories
- **View Statistics**: See expense count and total cost per category
- **Delete Categories**: Remove unused categories (expenses reassigned to "Other")

### Reports
- **Category Pie Chart**: Visual breakdown of expenses by category
- **Comparison Bar Chart**: Side-by-side comparison of projected vs actual costs
- **Interactive Charts**: Hover for detailed information

### Data Management
- **Excel Import**: Supports .xlsx and .xls files
- **JSON Export**: Export your data for backup or analysis
- **Local Storage**: Automatic saving of changes
- **Data Persistence**: Your data persists between browser sessions

## Customization

### Adding New Categories
1. Go to the Categories tab
2. Click "Add Category"
3. Enter the category name
4. Categories are immediately available for new expenses

### Modifying Styles
Edit `styles.css` to customize:
- Color scheme
- Layout and spacing
- Typography
- Responsive breakpoints

### Extending Functionality
The modular JavaScript structure makes it easy to add new features:
- Additional chart types
- Export formats
- Calculation methods
- UI components

## Troubleshooting

### Excel File Not Loading
- Ensure the file is in .xlsx or .xls format
- Check that the file structure matches the expected format
- Use the analyzer.html tool to debug file structure

### Charts Not Displaying
- Verify Chart.js library is loaded
- Check browser console for JavaScript errors
- Ensure data exists for chart generation

### Data Not Persisting
- Check if localStorage is enabled in your browser
- Verify you're accessing the app via HTTP (not file://)
- Clear browser cache if experiencing issues

## Contributing

This is a vanilla JavaScript project designed for simplicity and educational purposes. Feel free to:
- Report bugs
- Suggest improvements
- Submit pull requests
- Fork for your own modifications

## License

This project is open source and available under the MIT License.
