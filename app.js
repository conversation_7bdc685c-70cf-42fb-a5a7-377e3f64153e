// Household Budget Manager - Main Application
class BudgetApp {
    constructor() {
        this.data = {
            expenses: [],
            categories: ['Children', 'Utilities', 'Food', 'Transportation', 'Entertainment', 'Healthcare', 'Other'],
            budgetOverview: {
                projectedBalance: 0,
                actualBalance: 0
            }
        };
        
        this.currentView = 'dashboard';
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadFromLocalStorage();
        this.renderDashboard();
        this.loadExistingExcelFile();
    }

    setupEventListeners() {
        // Navigation
        document.querySelectorAll('.nav-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const view = e.target.dataset.view;
                this.switchView(view);
            });
        });

        // Excel file input
        document.getElementById('excel-file-input').addEventListener('change', (e) => {
            if (e.target.files[0]) {
                this.loadExcelFile(e.target.files[0]);
            }
        });

        // Forms
        document.getElementById('add-expense-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.addExpense();
        });

        document.getElementById('add-category-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.addCategory();
        });
    }

    switchView(viewName) {
        // Update navigation
        document.querySelectorAll('.nav-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-view="${viewName}"]`).classList.add('active');

        // Update views
        document.querySelectorAll('.view').forEach(view => {
            view.classList.remove('active');
        });
        document.getElementById(`${viewName}-view`).classList.add('active');

        this.currentView = viewName;

        // Render view-specific content
        switch(viewName) {
            case 'dashboard':
                this.renderDashboard();
                break;
            case 'expenses':
                this.renderExpenses();
                break;
            case 'categories':
                this.renderCategories();
                break;
            case 'reports':
                this.renderReports();
                break;
        }
    }

    async loadExistingExcelFile() {
        try {
            const response = await fetch('Household monthly budget1.xlsx');
            if (response.ok) {
                const arrayBuffer = await response.arrayBuffer();
                this.parseExcelData(arrayBuffer);
            }
        } catch (error) {
            console.log('No existing Excel file found, starting with empty data');
        }
    }

    async loadExcelFile(file) {
        this.showLoading(true);
        
        try {
            const arrayBuffer = await file.arrayBuffer();
            this.parseExcelData(arrayBuffer);
            this.showLoading(false);
            alert('Excel file loaded successfully!');
        } catch (error) {
            this.showLoading(false);
            alert('Error loading Excel file: ' + error.message);
        }
    }

    parseExcelData(arrayBuffer) {
        const workbook = XLSX.read(arrayBuffer, { type: 'array' });
        
        // Parse Budget Overview
        if (workbook.SheetNames.includes('Budget overview')) {
            this.parseBudgetOverview(workbook.Sheets['Budget overview']);
        }

        // Parse Monthly Expenses
        if (workbook.SheetNames.includes('Monthly expenses')) {
            this.parseMonthlyExpenses(workbook.Sheets['Monthly expenses']);
        }

        // Parse Additional Data for categories
        if (workbook.SheetNames.includes('Additional data')) {
            this.parseAdditionalData(workbook.Sheets['Additional data']);
        }

        this.saveToLocalStorage();
        this.renderCurrentView();
    }

    parseBudgetOverview(worksheet) {
        const data = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
        
        // Find projected and actual balance rows
        data.forEach(row => {
            if (row[1] && row[1].toString().toLowerCase().includes('projected balance')) {
                this.data.budgetOverview.projectedBalance = this.parseAmount(row[3]) || 0;
            }
            if (row[1] && row[1].toString().toLowerCase().includes('actual balance')) {
                this.data.budgetOverview.actualBalance = this.parseAmount(row[3]) || 0;
            }
        });
    }

    parseMonthlyExpenses(worksheet) {
        const data = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
        
        // Find header row
        let headerRowIndex = -1;
        for (let i = 0; i < data.length; i++) {
            if (data[i][1] && data[i][1].toString().toLowerCase().includes('description')) {
                headerRowIndex = i;
                break;
            }
        }

        if (headerRowIndex === -1) return;

        // Parse expenses
        this.data.expenses = [];
        for (let i = headerRowIndex + 1; i < data.length; i++) {
            const row = data[i];
            if (row[1] && row[1].toString().trim()) { // Has description
                const expense = {
                    id: Date.now() + Math.random(),
                    description: row[1].toString().trim(),
                    category: row[2] ? row[2].toString().trim() : 'Other',
                    projectedCost: this.parseAmount(row[3]) || 0,
                    actualCost: this.parseAmount(row[4]) || 0
                };
                expense.difference = expense.actualCost - expense.projectedCost;
                this.data.expenses.push(expense);
            }
        }
    }

    parseAdditionalData(worksheet) {
        const data = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
        
        // Extract categories
        const categories = new Set(this.data.categories);
        data.forEach(row => {
            if (row[4] && row[4].toString().trim() && 
                !row[4].toString().toLowerCase().includes('category') &&
                !row[4].toString().toLowerCase().includes('add')) {
                categories.add(row[4].toString().trim());
            }
        });
        this.data.categories = Array.from(categories);
    }

    parseAmount(value) {
        if (!value) return 0;
        const str = value.toString().replace(/[$,\s]/g, '');
        const num = parseFloat(str);
        return isNaN(num) ? 0 : num;
    }

    renderDashboard() {
        this.updateDashboardCards();
        this.renderRecentExpenses();
    }

    updateDashboardCards() {
        document.getElementById('projected-balance').textContent = 
            this.formatCurrency(this.data.budgetOverview.projectedBalance);
        
        document.getElementById('actual-balance').textContent = 
            this.formatCurrency(this.data.budgetOverview.actualBalance);

        const totalExpenses = this.data.expenses.reduce((sum, exp) => sum + exp.actualCost, 0);
        document.getElementById('total-expenses').textContent = 
            this.formatCurrency(totalExpenses);

        const savings = this.data.budgetOverview.actualBalance - totalExpenses;
        document.getElementById('savings').textContent = 
            this.formatCurrency(savings);
    }

    renderRecentExpenses() {
        const tbody = document.querySelector('#recent-expenses-table tbody');
        tbody.innerHTML = '';

        const recentExpenses = this.data.expenses.slice(0, 5);
        recentExpenses.forEach(expense => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${expense.description}</td>
                <td><span class="category-tag">${expense.category}</span></td>
                <td>${this.formatCurrency(expense.projectedCost)}</td>
                <td>${this.formatCurrency(expense.actualCost)}</td>
                <td class="${expense.difference >= 0 ? 'positive' : 'negative'}">
                    ${this.formatCurrency(expense.difference)}
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    renderExpenses() {
        const tbody = document.querySelector('#expenses-table tbody');
        tbody.innerHTML = '';

        this.data.expenses.forEach(expense => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${expense.description}</td>
                <td><span class="category-tag">${expense.category}</span></td>
                <td>${this.formatCurrency(expense.projectedCost)}</td>
                <td>${this.formatCurrency(expense.actualCost)}</td>
                <td class="${expense.difference >= 0 ? 'positive' : 'negative'}">
                    ${this.formatCurrency(expense.difference)}
                </td>
                <td>
                    <button class="btn btn-sm" onclick="app.editExpense('${expense.id}')">Edit</button>
                    <button class="btn btn-sm btn-danger" onclick="app.deleteExpense('${expense.id}')">Delete</button>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    renderCategories() {
        const grid = document.getElementById('categories-grid');
        grid.innerHTML = '';

        this.data.categories.forEach(category => {
            const categoryExpenses = this.data.expenses.filter(exp => exp.category === category);
            const totalCost = categoryExpenses.reduce((sum, exp) => sum + exp.actualCost, 0);
            
            const card = document.createElement('div');
            card.className = 'category-card';
            card.innerHTML = `
                <h4>${category}</h4>
                <div class="category-stats">
                    <div class="stat">
                        <span class="stat-label">Items:</span>
                        <span class="stat-value">${categoryExpenses.length}</span>
                    </div>
                    <div class="stat">
                        <span class="stat-label">Total:</span>
                        <span class="stat-value">${this.formatCurrency(totalCost)}</span>
                    </div>
                </div>
                <button class="btn btn-sm" onclick="app.deleteCategory('${category}')">Delete</button>
            `;
            grid.appendChild(card);
        });
    }

    renderCurrentView() {
        switch(this.currentView) {
            case 'dashboard':
                this.renderDashboard();
                break;
            case 'expenses':
                this.renderExpenses();
                break;
            case 'categories':
                this.renderCategories();
                break;
            case 'reports':
                this.renderReports();
                break;
        }
    }

    formatCurrency(amount) {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(amount);
    }

    showLoading(show) {
        const overlay = document.getElementById('loading-overlay');
        if (show) {
            overlay.classList.add('active');
        } else {
            overlay.classList.remove('active');
        }
    }

    saveToLocalStorage() {
        localStorage.setItem('budgetAppData', JSON.stringify(this.data));
    }

    loadFromLocalStorage() {
        const saved = localStorage.getItem('budgetAppData');
        if (saved) {
            this.data = { ...this.data, ...JSON.parse(saved) };
        }
    }
}

    renderReports() {
        // Destroy existing charts if they exist
        if (this.categoryChart) {
            this.categoryChart.destroy();
        }
        if (this.comparisonChart) {
            this.comparisonChart.destroy();
        }

        this.renderCategoryChart();
        this.renderComparisonChart();
    }

    renderCategoryChart() {
        const ctx = document.getElementById('category-chart').getContext('2d');

        // Aggregate expenses by category
        const categoryTotals = {};
        this.data.expenses.forEach(expense => {
            categoryTotals[expense.category] = (categoryTotals[expense.category] || 0) + expense.actualCost;
        });

        // Only create chart if there's data
        if (Object.keys(categoryTotals).length === 0) {
            ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);
            ctx.font = '16px Arial';
            ctx.fillStyle = '#666';
            ctx.textAlign = 'center';
            ctx.fillText('No expense data available', ctx.canvas.width / 2, ctx.canvas.height / 2);
            return;
        }

        this.categoryChart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: Object.keys(categoryTotals),
                datasets: [{
                    data: Object.values(categoryTotals),
                    backgroundColor: [
                        '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0',
                        '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF'
                    ]
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }

    renderComparisonChart() {
        const ctx = document.getElementById('comparison-chart').getContext('2d');

        const categories = [...new Set(this.data.expenses.map(exp => exp.category))];
        const projectedData = [];
        const actualData = [];

        categories.forEach(category => {
            const categoryExpenses = this.data.expenses.filter(exp => exp.category === category);
            const projected = categoryExpenses.reduce((sum, exp) => sum + exp.projectedCost, 0);
            const actual = categoryExpenses.reduce((sum, exp) => sum + exp.actualCost, 0);

            projectedData.push(projected);
            actualData.push(actual);
        });

        // Only create chart if there's data
        if (categories.length === 0) {
            ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);
            ctx.font = '16px Arial';
            ctx.fillStyle = '#666';
            ctx.textAlign = 'center';
            ctx.fillText('No expense data available', ctx.canvas.width / 2, ctx.canvas.height / 2);
            return;
        }

        this.comparisonChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: categories,
                datasets: [
                    {
                        label: 'Projected',
                        data: projectedData,
                        backgroundColor: '#36A2EB'
                    },
                    {
                        label: 'Actual',
                        data: actualData,
                        backgroundColor: '#FF6384'
                    }
                ]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return '$' + value.toLocaleString();
                            }
                        }
                    }
                }
            }
        });
    }

    addExpense() {
        const description = document.getElementById('expense-description').value;
        const category = document.getElementById('expense-category').value;
        const projected = parseFloat(document.getElementById('expense-projected').value) || 0;
        const actual = parseFloat(document.getElementById('expense-actual').value) || 0;

        const expense = {
            id: Date.now() + Math.random(),
            description,
            category,
            projectedCost: projected,
            actualCost: actual,
            difference: actual - projected
        };

        this.data.expenses.push(expense);
        this.saveToLocalStorage();
        this.renderCurrentView();
        this.closeModal('add-expense-modal');
        document.getElementById('add-expense-form').reset();
    }

    addCategory() {
        const name = document.getElementById('category-name').value.trim();
        if (name && !this.data.categories.includes(name)) {
            this.data.categories.push(name);
            this.saveToLocalStorage();
            this.updateCategorySelects();
            this.renderCurrentView();
        }
        this.closeModal('add-category-modal');
        document.getElementById('add-category-form').reset();
    }

    deleteExpense(id) {
        if (confirm('Are you sure you want to delete this expense?')) {
            this.data.expenses = this.data.expenses.filter(exp => exp.id != id);
            this.saveToLocalStorage();
            this.renderCurrentView();
        }
    }

    deleteCategory(category) {
        if (confirm(`Are you sure you want to delete the category "${category}"?`)) {
            this.data.categories = this.data.categories.filter(cat => cat !== category);
            // Update expenses with this category to "Other"
            this.data.expenses.forEach(exp => {
                if (exp.category === category) {
                    exp.category = 'Other';
                }
            });
            this.saveToLocalStorage();
            this.updateCategorySelects();
            this.renderCurrentView();
        }
    }

    updateCategorySelects() {
        const select = document.getElementById('expense-category');
        select.innerHTML = '';
        this.data.categories.forEach(category => {
            const option = document.createElement('option');
            option.value = category;
            option.textContent = category;
            select.appendChild(option);
        });
    }

    exportData() {
        const dataStr = JSON.stringify(this.data, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        const url = URL.createObjectURL(dataBlob);
        const link = document.createElement('a');
        link.href = url;
        link.download = 'budget-data.json';
        link.click();
        URL.revokeObjectURL(url);
    }

    clearAllData() {
        if (confirm('Are you sure you want to clear all data? This cannot be undone.')) {
            this.data = {
                expenses: [],
                categories: ['Children', 'Utilities', 'Food', 'Transportation', 'Entertainment', 'Healthcare', 'Other'],
                budgetOverview: {
                    projectedBalance: 0,
                    actualBalance: 0
                }
            };
            this.saveToLocalStorage();
            this.renderCurrentView();
        }
    }
}

// Global functions for HTML onclick handlers
function loadExcelFile() {
    document.getElementById('excel-file-input').click();
}

function exportData() {
    app.exportData();
}

function clearAllData() {
    app.clearAllData();
}

function showAddExpenseModal() {
    app.updateCategorySelects();
    showModal('add-expense-modal');
}

function showAddCategoryModal() {
    showModal('add-category-modal');
}

function showModal(modalId) {
    document.getElementById(modalId).classList.add('active');
}

function closeModal(modalId) {
    document.getElementById(modalId).classList.remove('active');
}

// Initialize the app
const app = new BudgetApp();
